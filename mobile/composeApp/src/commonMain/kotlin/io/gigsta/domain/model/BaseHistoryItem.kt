package io.gigsta.domain.model

import kotlinx.serialization.Serializable

sealed interface BaseHistoryItem {
    val id: String
    val type: HistoryItemType
    val createdAt: String
}

data class ResumeHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.CV_BUILDER,
    override val createdAt: String,
    val structuredData: StructuredResumeData?,
    val htmlContent: String?,
    val templateId: String,
    val templateName: String?,
    val tokensDeducted: Boolean
) : BaseHistoryItem

data class LetterHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.APPLICATION_LETTER,
    override val createdAt: String,
    val templateId: String,
    val templateName: String?,
    val plainText: String?,
    val designHtml: String?,
) : BaseHistoryItem

data class EmailHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.EMAIL_APPLICATION,
    override val createdAt: String,
    val subject: String,
    val body: String
) : BaseHistoryItem

// Structured Resume Data Classes (matching TypeScript StructuredResumeData)
@Serializable
data class StructuredResumeData(
    val personalInfo: PersonalInfo,
    val professionalSummary: String? = null,
    val targetPosition: String? = null,
    val experiences: List<Experience>? = null,
    val education: List<Education>? = null,
    val skills: Skills? = null,
    val certifications: List<Certification>? = null,
    val projects: List<Project>? = null,
    val languages: List<Language>? = null,
    val awards: List<Award>? = null,
    val metadata: Metadata? = null
)

@Serializable
data class PersonalInfo(
    val fullName: String? = null,
    val email: String? = null,
    val phone: String? = null,
    val linkedin: String? = null,
    val location: String? = null,
    val website: String? = null,
    val github: String? = null
)

@Serializable
data class Experience(
    val id: String,
    val jobTitle: String,
    val company: String,
    val location: String,
    val startDate: String,
    val endDate: String, // "Present" for current job
    val responsibilities: List<String> // Array of achievement bullets
)

@Serializable
data class Education(
    val id: String,
    val degree: String,
    val institution: String,
    val location: String? = null,
    val graduationDate: String,
    val gpa: String? = null,
    val relevantCoursework: List<String>? = null,
    val honors: List<String>? = null
)

@Serializable
data class Skills(
    val categories: List<SkillCategory>,
    val allSkills: List<String>? = null // Alternative flat structure for simpler templates
)

@Serializable
data class SkillCategory(
    val category: String,
    val skills: List<String> // Array of skills in this category
)

@Serializable
data class Certification(
    val id: String,
    val name: String,
    val issuer: String,
    val date: String,
    val credentialId: String? = null
)

@Serializable
data class Project(
    val id: String,
    val title: String,
    val description: String,
    val technologies: List<String>,
    val link: String? = null,
    val achievements: List<String>? = null
)

@Serializable
data class Language(
    val language: String,
    val proficiency: String
)

@Serializable
data class Award(
    val id: String,
    val title: String,
    val issuer: String,
    val date: String,
    val description: String? = null
)

@Serializable
data class Metadata(
    val generatedAt: String,
    val lastModified: String,
    val templateId: String? = null,
    val aiSuggestions: List<String>? = null
)

enum class HistoryItemType {
    CV_BUILDER,
    APPLICATION_LETTER,
    EMAIL_APPLICATION,
}